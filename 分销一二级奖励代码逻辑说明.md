# 分销一二级奖励代码逻辑说明文档

## 概述

本文档详细说明了商城系统中分销一二级奖励的代码逻辑运行机制，包括分销关系确定、佣金计算、发放时机和具体实现流程。

## 1. 核心架构

### 1.1 主要涉及文件
- `app/model/Payorder.php` - 订单支付处理，分销关系确定
- `app/common/Order.php` - 佣金发放核心逻辑
- `app/common/Member.php` - 会员佣金账户操作
- `app/controller/Cashier.php` - 收银台佣金计算
- `app/controller/ShopOrder.php` - 订单管理

### 1.2 核心数据表
- `member` - 会员表，存储推荐关系(pid字段)
- `member_level` - 会员等级表，存储分销权限和佣金比例
- `shop_order_goods` - 订单商品表，存储分销商ID和佣金金额
- `member_commission_record` - 佣金记录表，记录待发放和已发放佣金

## 2. 分销关系确定逻辑

### 2.1 分销商层级确定

当用户下单时，系统根据用户的推荐关系(`pid`字段)向上查找分销商：

```php
// 一级分销商确定
if ($member['pid']) {
    $parent1 = Db::name('member')->where('aid', $aid)->where('id', $member['pid'])->find();
    if ($parent1) {
        $agleveldata1 = Db::name('member_level')->where('aid', $aid)->where('id', $parent1['levelid'])->find();
        if ($agleveldata1 && $agleveldata1['can_agent'] != 0) {
            $ogdata['parent1'] = $parent1['id'];  // 一级分销商
        }
    }
}

// 二级分销商确定
if (isset($parent1) && $parent1['pid']) {
    $parent2 = Db::name('member')->where('aid', $aid)->where('id', $parent1['pid'])->find();
    if ($parent2) {
        $agleveldata2 = Db::name('member_level')->where('aid', $aid)->where('id', $parent2['levelid'])->find();
        if ($agleveldata2 && $agleveldata2['can_agent'] > 1) {
            $ogdata['parent2'] = $parent2['id'];  // 二级分销商
        }
    }
}
```

### 2.2 分销资格判断

会员等级表中的`can_agent`字段决定分销资格：
- `can_agent = 0`：无分销资格
- `can_agent = 1`：可做一级分销商
- `can_agent > 1`：可做二级分销商  
- `can_agent > 2`：可做三级分销商

## 3. 佣金计算逻辑

### 3.1 计算方式分类

系统支持多种佣金计算方式，根据商品的`commissionset`字段决定：

#### 3.1.1 按比例计算 (commissionset=1)
```php
if ($form['commissionset'] == 1) { // 按比例
    $commissiondata = json_decode($form['commissiondata1'], true);
    if ($commissiondata && $agleveldata1 && $agleveldata2) {
        $ogdata['parent1commission'] = isset($commissiondata[$agleveldata1['id']]['commission1']) 
            ? $commissiondata[$agleveldata1['id']]['commission1'] * $order['money'] * 0.01 
            : 0;
        $ogdata['parent2commission'] = isset($commissiondata[$agleveldata2['id']]['commission2']) 
            ? $commissiondata[$agleveldata2['id']]['commission2'] * $order['money'] * 0.01 
            : 0;
    }
}
```

#### 3.1.2 默认按等级比例计算
```php
else { // 默认按比例
    if ($agleveldata1 && $agleveldata2) {
        $ogdata['parent1commission'] = $agleveldata1['commission1'] * $order['money'] * 0.01;
        $ogdata['parent2commission'] = $agleveldata2['commission2'] * $order['money'] * 0.01;
    }
}
```

#### 3.1.3 按固定金额计算 (commissionset=2)
```php
elseif($product['commissionset']==2){//按固定金额
    $commissiondata = json_decode($product['commissiondata2'],true);
    if($commissiondata){
        $commission = $commissiondata[$userlevel['id']]['commission1'];
    }
}
```

### 3.2 特殊处理逻辑

#### 3.2.1 自购返佣
```php
// 设置自己拿一级分销的时候，自己下单了，自己拿一级分销
$agleveldata = Db::name('member_level')->where('aid',aid)->where('id',$member['levelid'])->find();
if($agleveldata['can_agent'] > 0 && $agleveldata['commission1own']==1){
    $member['pid'] = $member['id'];
}
```

#### 3.2.2 极差分销
系统支持极差分销模式，通过`fenxiao_manage_status`开关控制：
```php
if($sysset['fenxiao_manage_status']){
    $commission_data = \app\common\Fenxiao::fenxiao_jicha($sysset,$member,$product,$num,$commission_totalprice);
}else{
    $commission_data = \app\common\Fenxiao::fenxiao($sysset,$member,$product,$num,$commission_totalprice,$isfg,$istc1,$istc2,$istc3);
}
```

## 4. 佣金发放机制

### 4.1 发放时机控制

系统通过`admin_set`表中的配置控制佣金发放时机：

```php
$set = Db::name('admin_set')->where('aid',$aid)->find();
if($set['fxjiesuantime_delaydays'] == '0'){ //确认收货后发佣金
    self::giveCommission($order,$type);
}
```

发放时机选项：
- `fxjiesuantime = 0`：确认收货后发放
- `fxjiesuantime = 1`：付款后发放
- `fxjiesuantime_delaydays`：延迟天数设置

### 4.2 定时发放处理

系统通过定时任务处理延迟发放的佣金：

```php
public static function commissionDelayGive($aid){
    $set = Db::name('admin_set')->where('aid',$aid)->find();
    $dtime = time() - $set['fxjiesuantime_delaydays'] * 24 * 3600;
    
    $records = Db::name('member_commission_record')
        ->where('aid',$aid)
        ->where('status',0)
        ->where('createtime','<',$dtime)
        ->select();
        
    foreach($records as $record){
        // 检查订单状态并发放佣金
        if(($set['fxjiesuantime'] == 0 && $status==3 && $order['paytime'] < $dtime) || 
           ($set['fxjiesuantime'] == 1 && in_array($status,[1,2,3]) && $order['paytime'] < $dtime)){
            self::giveCommission($order,$record['type']);
        }
    }
}
```

### 4.3 实际发放执行

`giveCommission`方法负责实际的佣金发放：

```php
public static function giveCommission($order,$type='shop'){
    $aid = $order['aid'];
    if($type == 'shop'){
        $commission_record_list = Db::name('member_commission_record')
            ->where('aid',$aid)
            ->where('type','shop')
            ->where('orderid',$order['id'])
            ->where('status',0)
            ->select();
            
        foreach($commission_record_list as $commission_record){
            // 更新记录状态
            Db::name('member_commission_record')
                ->where('id',$commission_record['id'])
                ->update(['status'=>1,'endtime'=>time()]);
                
            // 发放佣金到账户
            if($commission_record['commission'] > 0){
                \app\common\Member::addcommission($aid,$commission_record['mid'],
                    $commission_record['frommid'],$commission,$commission_record['remark']);
                    
                // 发送通知
                $this->sendCommissionNotification($commission_record);
            }
        }
    }
}
```

## 5. 数据流转过程

### 5.1 完整流程图

```
用户下单
    ↓
确定分销关系(parent1/parent2/parent3)
    ↓
计算佣金金额(parent1commission/parent2commission)
    ↓
创建佣金记录(member_commission_record, status=0)
    ↓
订单状态变更触发
    ↓
执行giveCommission方法
    ↓
更新记录状态(status=1)
    ↓
调用Member::addcommission发放佣金
    ↓
发送通知(微信模板消息/短信)
```

### 5.2 关键状态管理

#### 佣金记录状态：
- `status = 0`：待发放
- `status = 1`：已发放
- `status = 2`：已取消

#### 订单商品状态：
- `iscommission = 0`：未发放佣金
- `iscommission = 1`：已发放佣金

## 6. 特殊功能模块

### 6.1 收益池机制

当开启收益池功能时，佣金发放会受到收益池余额限制：

```php
$memberlevel = Db::name('member_level')->where('aid',$aid)->where('id',$member['levelid'])->find();
if($memberlevel['syc'] == 1) {
    if($member['syc'] >= $commission) {
        $yj = $commission;
        \app\common\Member::addsyc($aid,$mid,'-'.$yj,'佣金到账扣除'.$yj);
    } else {
        $syc_shouyi = json_decode($memberlevel['syc_shouyi'],1);
        $gwjf = $commission * $syc_shouyi['gw'] * 0.01;
        $yj = $commission * $syc_shouyi['yj'] * 0.01;
        $commission = $yj;
        self::addhei($aid, $mid, $gwjf, '佣金到账,收益池不足,到账金额'.$gwjf);
    }
}
```

### 6.2 联动冻结机制

系统支持佣金联动冻结功能：

```php
$liandongrs = Db::name('liandong_set')->where('aid',$aid)->find();
if($commission > 0 && $liandongrs['status'] == 1 && $liandongrs['fcj_bili'] != 0) {
    $fcj_levelids = explode(',',$liandongrs['fcj_levelids']);
    if(in_array('-1',$fcj_levelids) || in_array($member['levelid'],$fcj_levelids)) {
        $dongjie_num = $commission * $liandongrs['fcj_bili'] * 0.01;
        $commission = $commission - $dongjie_num;
        self::adddongjienum($aid,$mid,$dongjie_num,$remark.',冻结,冻结比例'.$liandongrs['fcj_bili'].'%','');
    }
}
```

## 7. 通知机制

### 7.1 微信模板消息

```php
$tmplcontent = [];
$tmplcontent['first'] = '恭喜您，成功分销商品获得'.t('佣金').'：￥'.$commission_record['commission'];
$tmplcontent['remark'] = '点击进入查看~';
$tmplcontent['keyword1'] = $og['name']; //商品信息
$tmplcontent['keyword2'] = (string) $og['sell_price'];//商品单价
$tmplcontent['keyword3'] = $commission_record['commission'].'元';//商品佣金
$tmplcontent['keyword4'] = date('Y-m-d H:i:s',$commission_record['createtime']);//分销时间
$rs = \app\common\Wechat::sendtmpl($aid,$commission_record['mid'],'tmpl_fenxiaosuccess',$tmplcontent,m_url('pages/my/usercenter', $aid));
```

### 7.2 短信通知

```php
$tel = Db::name('member')->where('id',$commission_record['mid'])->value('tel');
$rs = \app\common\Sms::send($aid,$tel,'tmpl_fenxiaosuccess',['money'=>$commission_record['commission']]);
```

## 8. 重要配置参数

### 8.1 系统级配置 (admin_set表)
- `fxjiesuantime`：佣金结算时机 (0=确认收货后, 1=付款后)
- `fxjiesuantime_delaydays`：延迟发放天数
- `fenxiao_manage_status`：是否启用极差分销
- `fxjiesuantype`：佣金计算基数 (1=售价, 2=售价-成本价)

### 8.2 会员等级配置 (member_level表)
- `can_agent`：分销资格等级
- `commission1`：一级佣金比例
- `commission2`：二级佣金比例
- `commission3`：三级佣金比例
- `commission1own`：是否支持自购返佣

### 8.3 商品配置
- `commissionset`：佣金设置类型 (-1=不参与分销, 1=按比例, 2=固定金额)
- `commissiondata1`：按比例佣金配置数据
- `commissiondata2`：固定金额佣金配置数据

## 9. 常见问题处理

### 9.1 佣金计算异常
- 检查会员等级的`can_agent`设置
- 检查商品的`commissionset`配置
- 检查佣金比例设置是否正确

### 9.2 佣金发放延迟
- 检查`fxjiesuantime_delaydays`设置
- 检查定时任务是否正常运行
- 检查订单状态是否符合发放条件

### 9.3 分销关系错误
- 检查会员的`pid`字段是否正确
- 检查推荐关系链是否完整
- 检查会员等级的分销权限设置

## 10. 总结

分销一二级奖励系统是一个完整的多级分销解决方案，具有以下特点：

1. **灵活的分销层级**：支持一级、二级、三级分销
2. **多样的计算方式**：支持按比例、固定金额、商品单独设置
3. **完善的状态管理**：通过数据库状态字段确保发放准确性
4. **丰富的特殊功能**：收益池、联动冻结、极差分销等
5. **完整的通知机制**：微信模板消息和短信通知
6. **可配置的发放时机**：支持即时发放和延迟发放

该系统设计合理，代码结构清晰，能够满足各种复杂的分销业务需求。
